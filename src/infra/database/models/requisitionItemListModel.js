module.exports = (sequelize, Sequelize) => {
  const RequisitionItemListModel = sequelize.define(
    'requisition_item_lists',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
      },
      itemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'item_id',
      },
      itemType: {
        type: Sequelize.TEXT,
        allowNull: false,
        field: 'item_type',
      },
      quantity: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        field: 'quantity',
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('quantity', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('quantity');
          return parseFloat(rawValue || 0);
        },
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'notes',
      },
      accountCode: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'account_code',
      },
      ofmListId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'ofm_list_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },

      itemDetails: {
        type: Sequelize.VIRTUAL,
        get() {
          switch (this.itemType) {
            case 'ofm':
              return this.item;
            case 'non-ofm':
              return this.nonOfmItem;
            case 'ofm-tom':
              return this.item;
            case 'non-ofm-tom':
              return this.nonOfmItem;
            default:
              return null;
          }
        },
      },
    },
    {
      timestamps: true,
      indexes: [
        {
          unique: true,
          fields: ['requisition_id', 'item_id'],
          name: 'unique_requisition_item_per_requisition',
        },
      ],
    },
  );

  RequisitionItemListModel.associate = (models) => {
    RequisitionItemListModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    RequisitionItemListModel.belongsTo(models.itemModel, {
      foreignKey: 'itemId',
      as: 'item',
    });

    RequisitionItemListModel.belongsTo(models.nonOfmItemModel, {
      foreignKey: 'itemId',
      as: 'nonOfmItem',
    });
  };

  const createRequisitionItemHistoryRecord = async (
    requisitionItem,
    options,
  ) => {
    const { transaction, userId = null } = options;

    const isOfm = requisitionItem.dataValues.itemType === 'ofm';

    try {
      const item = await sequelize
        .model(isOfm ? 'items' : 'non_ofm_items')
        .findOne({
          attributes: [isOfm ? 'itmDes' : 'itemName'],
          where: { id: requisitionItem.dataValues.itemId },
        });

      const deliveryReceiptItem = await sequelize
        .model('delivery_receipt_items')
        .findOne({
          attributes: ['qtyOrdered', 'qtyDelivered'],
          where: {
            itemId: requisitionItem.itemId,
          },
        });

      await sequelize.model('requisition_item_histories').create(
        {
          requisitionId: requisitionItem.requisitionId,
          item:
            item?.itmDes ||
            item?.itemName ||
            `Unknown Item (ID: ${requisitionItem.dataValues.itemId})`,
          quantityRequested: requisitionItem.quantity,
          quantityOrdered: 0,
          quantityDelivered: 0,
          ...(userId ? { lastUpdatedBy: userId } : {}),
        },
        { transaction },
      );
    } catch (error) {
      console.error(
        'HOOK_ERROR - requisitionItemListModel - createRequisitionItemHistoryRecord: ',
        error.stack,
      );
    }
  };

  // TODO: request history for items (quantity_requested)
  RequisitionItemListModel.afterCreate(createRequisitionItemHistoryRecord);
  RequisitionItemListModel.afterUpdate(createRequisitionItemHistoryRecord);

  return RequisitionItemListModel;
};
